{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/styles/access-error-styles.ts", "turbopack:///[project]/node_modules/next/src/client/components/http-access-fallback/error-fallback.tsx", "turbopack:///[project]/node_modules/next/src/client/components/builtin/not-found.tsx"], "sourcesContent": ["export const styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n\n  desc: {\n    display: 'inline-block',\n  },\n\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    padding: '0 23px 0 0',\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n    lineHeight: '49px',\n  },\n\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '49px',\n    margin: 0,\n  },\n}\n", "import { styles } from '../styles/access-error-styles'\n\nexport function HTTPAccessErrorFallback({\n  status,\n  message,\n}: {\n  status: number\n  message: string\n}) {\n  return (\n    <>\n      {/* <head> */}\n      <title>{`${status}: ${message}`}</title>\n      {/* </head> */}\n      <div style={styles.error}>\n        <div>\n          <style\n            dangerouslySetInnerHTML={{\n              /* Minified CSS from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                @media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }\n              */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`,\n            }}\n          />\n          <h1 className=\"next-error-h1\" style={styles.h1}>\n            {status}\n          </h1>\n          <div style={styles.desc}>\n            <h2 style={styles.h2}>{message}</h2>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n", "import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function NotFound() {\n  return (\n    <HTTPAccessErrorFallback\n      status={404}\n      message=\"This page could not be found.\"\n    />\n  )\n}\n"], "names": ["styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "h1", "margin", "padding", "fontSize", "fontWeight", "verticalAlign", "lineHeight", "h2", "HTTPAccessErrorFallback", "status", "message", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className", "NotFound"], "mappings": "sHAAaA,SAAAA,qCAAAA,KAAN,IAAMA,EAA8C,CACzDC,MAAO,CAELC,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EAEAC,KAAM,CACJJ,QAAS,cACX,EAEAK,GAAI,CACFL,QAAS,eACTM,OAAQ,aACRC,QAAS,aACTC,SAAU,GACVC,WAAY,IACZC,cAAe,MACfC,WAAY,MACd,EAEAC,GAAI,CACFJ,SAAU,GACVC,WAAY,IACZE,WAAY,OACZL,OAAQ,CACV,CACF,gUC/BgBO,0BAAAA,qCAAAA,yBAFO,CAAA,CAAA,IAAA,GAEhB,SAASA,EAAwB,CAMvC,EANuC,GAAA,QACtCC,CAAM,SACNC,CAAO,CAIR,CANuC,EAOtC,MACE,CADF,AACE,EAAA,EAAA,IAAA,EAAA,CADF,CACE,QAAA,CAAA,WAEE,CAAA,EAAA,EAAA,GAAA,EAACC,QAAAA,UAAUF,EAAO,KAAIC,IAEtB,CAAA,EAAA,EAAA,GAAA,EAACE,MAAAA,CAAIC,MAAOvB,EAAAA,MAAM,CAACC,KAAK,UACtB,CAAA,EAAA,EAAA,IAAA,EAACqB,CAAD,KAACA,WACC,CAAA,EAAA,EAAA,GAAA,EAACC,QAAAA,CACCC,wBAAyB,CAcvBC,OAAS,+NACX,IAEF,CAAA,EAAA,EAAA,GAAA,EAACf,KAAAA,CAAGgB,UAAU,gBAAgBH,MAAOvB,EAAAA,MAAM,CAACU,EAAE,UAC3CS,IAEH,CAAA,EAAA,EAAA,GAAA,EAACG,MAAAA,CAAIC,MAAOvB,EAAAA,MAAM,CAACS,IAAI,UACrB,CAAA,EAAA,EAAA,GAAA,EAACQ,EAAD,GAACA,CAAGM,MAAOvB,EAAAA,MAAM,CAACiB,EAAE,UAAGG,aAMnC,+TC1CA,UAAA,qCAAwBO,yBAFgB,CAAA,CAAA,IAAA,GAEzB,SAASA,IACtB,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAACT,EADH,AACGA,uBAAuB,CAAA,CACtBC,OAAQ,IACRC,QAAQ,iCAGd", "ignoreList": [0, 1, 2]}