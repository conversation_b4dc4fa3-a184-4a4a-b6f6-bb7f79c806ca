{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/our-work", "regex": "^/our\\-work(?:/)?$", "routeKeys": {}, "namedRegex": "^/our\\-work(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/services/bathroom-renovations", "regex": "^/services/bathroom\\-renovations(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/bathroom\\-renovations(?:/)?$"}, {"page": "/services/external-wall-insulation", "regex": "^/services/external\\-wall\\-insulation(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/external\\-wall\\-insulation(?:/)?$"}, {"page": "/services/general-renovations", "regex": "^/services/general\\-renovations(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/general\\-renovations(?:/)?$"}, {"page": "/services/kitchen-fitting", "regex": "^/services/kitchen\\-fitting(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/kitchen\\-fitting(?:/)?$"}, {"page": "/services/loft-conversions", "regex": "^/services/loft\\-conversions(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/loft\\-conversions(?:/)?$"}, {"page": "/testimonials", "regex": "^/testimonials(?:/)?$", "routeKeys": {}, "namedRegex": "^/testimonials(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "rsc", "varyHeader": "rsc, next-router-state-tree, next-router-prefetch, next-router-segment-prefetch", "prefetchHeader": "next-router-prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "next-router-segment-prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}